name: Release stdio2sse

on:
  push:
    tags:
      - 'stdio2sse-v*'
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag to release (e.g., stdio2sse-v0.1.0)'
        required: true
        type: string

env:
  GO_VERSION: "1.23"

jobs:
  release:
    name: Create stdio2sse Release
    runs-on: ubuntu-latest
    permissions:
      contents: write
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Get version
      id: version
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          echo "version=${{ github.event.inputs.tag }}" >> $GITHUB_OUTPUT
        else
          echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
        fi

    - name: Cache Go modules
      uses: actions/cache@v4
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Run stdio2sse tests
      working-directory: ./stdio2sse
      run: go test -v ./...

    - name: Build stdio2sse for multiple platforms
      working-directory: ./stdio2sse
      run: |
        mkdir -p ../dist
        
        VERSION="${{ steps.version.outputs.version }}"
        LDFLAGS="-s -w -X main.version=${VERSION}"
        
        # macOS
        GOOS=darwin GOARCH=arm64 go build -ldflags="${LDFLAGS}" -o ../dist/stdio2sse-darwin-arm64 .
        GOOS=darwin GOARCH=amd64 go build -ldflags="${LDFLAGS}" -o ../dist/stdio2sse-darwin-amd64 .
        
        # Linux
        GOOS=linux GOARCH=arm64 go build -ldflags="${LDFLAGS}" -o ../dist/stdio2sse-linux-arm64 .
        GOOS=linux GOARCH=amd64 go build -ldflags="${LDFLAGS}" -o ../dist/stdio2sse-linux-amd64 .
        
        # Windows
        GOOS=windows GOARCH=amd64 go build -ldflags="${LDFLAGS}" -o ../dist/stdio2sse-windows-amd64.exe .

    - name: Create archives
      run: |
        cd dist
        
        # stdio2sse archives
        tar -czf stdio2sse-darwin-arm64.tar.gz stdio2sse-darwin-arm64
        tar -czf stdio2sse-darwin-amd64.tar.gz stdio2sse-darwin-amd64
        tar -czf stdio2sse-linux-arm64.tar.gz stdio2sse-linux-arm64
        tar -czf stdio2sse-linux-amd64.tar.gz stdio2sse-linux-amd64
        zip stdio2sse-windows-amd64.zip stdio2sse-windows-amd64.exe

    - name: Generate checksums
      run: |
        cd dist
        sha256sum *.tar.gz *.zip > checksums.txt

    - name: Generate changelog
      id: changelog
      run: |
        if git tag --list | grep -q "stdio2sse-v"; then
          PREV_TAG=$(git tag -l "stdio2sse-v*" | sort -V | tail -n2 | head -n1)
          if [ -n "$PREV_TAG" ]; then
            echo "changelog<<EOF" >> $GITHUB_OUTPUT
            git log --pretty=format:"- %s (%h)" $PREV_TAG..HEAD -- stdio2sse/ >> $GITHUB_OUTPUT
            echo "" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT
          else
            echo "changelog=Initial stdio2sse release" >> $GITHUB_OUTPUT
          fi
        else
          echo "changelog=Initial stdio2sse release" >> $GITHUB_OUTPUT
        fi

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ steps.version.outputs.version }}
        name: stdio2sse ${{ steps.version.outputs.version }}
        body: |
          ## stdio2sse Changes
          ${{ steps.changelog.outputs.changelog }}
          
          ## Installation
          
          ### Quick Install
          
          ```bash
          curl -sSL https://raw.githubusercontent.com/eliezedeck/AIDevTools/main/stdio2sse/install.sh | bash
          ```
          
          ### Manual Download
          
          Download the appropriate binary for your platform from the assets below.
          
          ## Verification
          
          Verify the download with the provided checksums:
          ```bash
          sha256sum -c checksums.txt
          ```
        files: |
          dist/*.tar.gz
          dist/*.zip
          dist/checksums.txt
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}