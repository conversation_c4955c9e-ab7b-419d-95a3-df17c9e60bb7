name: CI

on:
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  GO_VERSION: "1.23"

jobs:
  test:
    name: Test
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v4
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Download sidekick dependencies
      working-directory: ./sidekick
      run: go mod download

    - name: Verify sidekick dependencies
      working-directory: ./sidekick
      run: go mod verify

    - name: Download stdio2sse dependencies
      working-directory: ./stdio2sse
      run: go mod download

    - name: Verify stdio2sse dependencies
      working-directory: ./stdio2sse
      run: go mod verify

    - name: Run sidekick tests
      working-directory: ./sidekick
      run: |
        if [ "$RUNNER_OS" == "Linux" ]; then
          go test -v -race -coverprofile=coverage.out ./...
        else
          go test -v ./...
        fi
      shell: bash

    - name: Run stdio2sse tests
      working-directory: ./stdio2sse
      run: go test -v ./...

    - name: Build sidekick
      working-directory: ./sidekick
      run: go build -v -o sidekick .

    - name: Build stdio2sse
      working-directory: ./stdio2sse
      run: go build -v -o stdio2sse .

    - name: Upload coverage to Codecov
      if: matrix.os == 'ubuntu-latest'
      uses: codecov/codecov-action@v4
      with:
        file: ./sidekick/coverage.out
        flags: unittests
        name: codecov-umbrella


